<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Захист Села - Tower Defense</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            overflow: hidden;
            height: 100vh;
        }

        .game-container {
            display: flex;
            height: 100vh;
            gap: 5px;
            padding: 5px;
        }

        .game-board {
            flex: 1;
            background: #27ae60;
            border: 2px solid #2c3e50;
            border-radius: 5px;
            position: relative;
            overflow: hidden;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            grid-template-rows: repeat(12, 1fr);
            height: 100%;
            gap: 1px;
        }

        .cell {
            background: #2ecc71;
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
        }

        .cell:hover {
            background: #3498db;
        }

        .cell.path {
            background: #8b4513;
        }

        .cell.spawn {
            background: #e74c3c;
        }

        .cell.village {
            background: #f39c12;
        }

        .tower {
            width: 80%;
            height: 80%;
            border-radius: 50%;
            position: absolute;
            top: 10%;
            left: 10%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .tower.basic {
            background: #95a5a6;
            color: #2c3e50;
        }

        .tower.splash {
            background: #e67e22;
            color: white;
        }

        .tower.slow {
            background: #3498db;
            color: white;
        }

        .monster {
            width: 70%;
            height: 70%;
            border-radius: 50%;
            position: absolute;
            top: 15%;
            left: 15%;
            transition: all 0.3s linear;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        .monster.basic {
            background: #e74c3c;
            color: white;
        }

        .monster.fast {
            background: #9b59b6;
            color: white;
        }

        .monster.tank {
            background: #34495e;
            color: white;
        }

        .health-bar {
            position: absolute;
            top: -5px;
            left: 10%;
            width: 80%;
            height: 3px;
            background: #e74c3c;
            border-radius: 2px;
        }

        .health-fill {
            height: 100%;
            background: #27ae60;
            border-radius: 2px;
            transition: width 0.2s;
        }

        .control-panel {
            width: 250px;
            background: #34495e;
            border-radius: 5px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .stats {
            background: #2c3e50;
            padding: 8px;
            border-radius: 3px;
            font-size: 14px;
        }

        .tower-shop {
            background: #2c3e50;
            padding: 8px;
            border-radius: 3px;
        }

        .tower-btn {
            width: 100%;
            padding: 8px;
            margin: 2px 0;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .tower-btn:hover {
            background: #2980b9;
        }

        .tower-btn:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
        }

        .tower-btn.selected {
            background: #e67e22;
        }

        .game-controls {
            display: flex;
            gap: 5px;
        }

        .control-btn {
            flex: 1;
            padding: 8px;
            background: #27ae60;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .control-btn:hover {
            background: #229954;
        }

        .control-btn.pause {
            background: #f39c12;
        }

        .wave-info {
            background: #2c3e50;
            padding: 8px;
            border-radius: 3px;
            text-align: center;
            font-size: 14px;
        }

        .language-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .projectile {
            width: 4px;
            height: 4px;
            background: #f1c40f;
            border-radius: 50%;
            position: absolute;
            z-index: 10;
        }

        .explosion {
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, #f39c12, transparent);
            border-radius: 50%;
            position: absolute;
            animation: explode 0.3s ease-out;
            z-index: 5;
        }

        @keyframes explode {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(1); opacity: 0; }
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 100;
            display: none;
        }

        .range-indicator {
            position: absolute;
            border: 2px solid rgba(52, 152, 219, 0.5);
            border-radius: 50%;
            pointer-events: none;
            z-index: 5;
        }
    </style>
</head>
<body>
    <button class="language-toggle" onclick="toggleLanguage()">EN</button>
    
    <div class="game-container">
        <div class="game-board">
            <div class="grid" id="gameGrid"></div>
            <div class="game-over" id="gameOver">
                <h2 id="gameOverTitle">Гра закінчена!</h2>
                <p id="gameOverText">Ваше село було знищено!</p>
                <button class="control-btn" onclick="restartGame()" id="restartBtn">Почати знову</button>
            </div>
        </div>
        
        <div class="control-panel">
            <div class="stats">
                <div id="health">❤️ <span id="healthText">Здоров'я</span>: <span id="healthValue">100</span></div>
                <div id="coins">💰 <span id="coinsText">Монети</span>: <span id="coinsValue">100</span></div>
                <div id="score">⭐ <span id="scoreText">Рахунок</span>: <span id="scoreValue">0</span></div>
            </div>
            
            <div class="wave-info">
                <div id="waveText">Хвиля</div>: <span id="waveNumber">1</span>
                <div id="enemiesText">Вороги</div>: <span id="enemiesLeft">10</span>
            </div>
            
            <div class="tower-shop">
                <h3 id="shopTitle">Магазин веж</h3>
                <button class="tower-btn" id="basicTowerBtn" onclick="selectTower('basic')">
                    🏹 <span id="basicTowerText">Базова</span> (50💰)
                </button>
                <button class="tower-btn" id="splashTowerBtn" onclick="selectTower('splash')">
                    💥 <span id="splashTowerText">Вибухова</span> (100💰)
                </button>
                <button class="tower-btn" id="slowTowerBtn" onclick="selectTower('slow')">
                    ❄️ <span id="slowTowerText">Уповільнююча</span> (75💰)
                </button>
            </div>
            
            <div class="game-controls">
                <button class="control-btn" id="startWaveBtn" onclick="startWave()">
                    <span id="startWaveText">Почати хвилю</span>
                </button>
                <button class="control-btn pause" id="pauseBtn" onclick="togglePause()">
                    <span id="pauseText">Пауза</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            health: 100,
            coins: 100,
            score: 0,
            wave: 1,
            enemiesInWave: 10,
            enemiesLeft: 10,
            selectedTower: null,
            isPaused: false,
            isGameOver: false,
            language: 'uk'
        };

        // Game objects
        let towers = [];
        let monsters = [];
        let projectiles = [];
        let grid = [];
        let path = [];

        // Game constants
        const GRID_WIDTH = 20;
        const GRID_HEIGHT = 12;
        const CELL_SIZE = 30;

        // Tower types
        const TOWER_TYPES = {
            basic: { cost: 50, damage: 25, range: 2, fireRate: 1000, color: '#95a5a6' },
            splash: { cost: 100, damage: 40, range: 1.5, fireRate: 1500, color: '#e67e22', splash: true },
            slow: { cost: 75, damage: 15, range: 2.5, fireRate: 800, color: '#3498db', slow: true }
        };

        // Monster types
        const MONSTER_TYPES = {
            basic: { health: 100, speed: 1, reward: 10, color: '#e74c3c' },
            fast: { health: 60, speed: 2, reward: 15, color: '#9b59b6' },
            tank: { health: 200, speed: 0.5, reward: 25, color: '#34495e' }
        };

        // Language translations
        const translations = {
            uk: {
                health: "Здоров'я",
                coins: "Монети", 
                score: "Рахунок",
                wave: "Хвиля",
                enemies: "Вороги",
                shopTitle: "Магазин веж",
                basicTower: "Базова",
                splashTower: "Вибухова", 
                slowTower: "Уповільнююча",
                startWave: "Почати хвилю",
                pause: "Пауза",
                resume: "Продовжити",
                gameOver: "Гра закінчена!",
                villageDestroyed: "Ваше село було знищено!",
                victory: "Перемога!",
                allWavesDefeated: "Ви перемогли всі хвилі!",
                restart: "Почати знову"
            },
            en: {
                health: "Health",
                coins: "Coins",
                score: "Score", 
                wave: "Wave",
                enemies: "Enemies",
                shopTitle: "Tower Shop",
                basicTower: "Basic",
                splashTower: "Splash",
                slowTower: "Slow",
                startWave: "Start Wave",
                pause: "Pause",
                resume: "Resume", 
                gameOver: "Game Over!",
                villageDestroyed: "Your village was destroyed!",
                victory: "Victory!",
                allWavesDefeated: "You defeated all waves!",
                restart: "Restart"
            }
        };

        // Initialize game
        function initGame() {
            createGrid();
            createPath();
            updateUI();
            updateLanguage();
        }

        // Create game grid
        function createGrid() {
            const gameGrid = document.getElementById('gameGrid');
            gameGrid.innerHTML = '';
            grid = [];

            for (let y = 0; y < GRID_HEIGHT; y++) {
                grid[y] = [];
                for (let x = 0; x < GRID_WIDTH; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    cell.addEventListener('click', () => placeTower(x, y));
                    cell.addEventListener('mouseenter', () => showTowerRange(x, y));
                    cell.addEventListener('mouseleave', () => hideTowerRange());
                    gameGrid.appendChild(cell);
                    grid[y][x] = { element: cell, tower: null, monster: null };
                }
            }
        }

        // Create path from spawn to village
        function createPath() {
            path = [];
            // Create a winding path from left to right
            let x = 0, y = 6; // Start middle-left

            while (x < GRID_WIDTH - 1) {
                path.push({x, y});
                grid[y][x].element.classList.add('path');

                if (x === 0) {
                    grid[y][x].element.classList.add('spawn');
                }

                // Create some curves in the path
                if (x === 5 && y > 2) y--;
                else if (x === 10 && y < GRID_HEIGHT - 3) y++;
                else if (x === 15 && y > 3) y--;

                x++;
            }

            // Add final village cell
            path.push({x: GRID_WIDTH - 1, y});
            grid[y][GRID_WIDTH - 1].element.classList.add('path');
            grid[y][GRID_WIDTH - 1].element.classList.add('village');
        }

        // Tower placement
        function placeTower(x, y) {
            if (!gameState.selectedTower || gameState.isGameOver) return;

            const cell = grid[y][x];
            if (cell.tower || cell.element.classList.contains('path')) return;

            const towerType = TOWER_TYPES[gameState.selectedTower];
            if (gameState.coins < towerType.cost) return;

            // Create tower
            const tower = {
                x, y, type: gameState.selectedTower,
                damage: towerType.damage,
                range: towerType.range,
                fireRate: towerType.fireRate,
                lastFire: 0,
                target: null,
                element: createTowerElement(gameState.selectedTower)
            };

            cell.element.appendChild(tower.element);
            cell.tower = tower;
            towers.push(tower);

            gameState.coins -= towerType.cost;
            updateUI();
        }

        // Create tower visual element
        function createTowerElement(type) {
            const element = document.createElement('div');
            element.className = `tower ${type}`;

            const symbols = { basic: '🏹', splash: '💥', slow: '❄️' };
            element.textContent = symbols[type];

            return element;
        }

        // Show tower range on hover
        function showTowerRange(x, y) {
            if (!gameState.selectedTower) return;

            const towerType = TOWER_TYPES[gameState.selectedTower];
            const range = towerType.range;

            const indicator = document.createElement('div');
            indicator.className = 'range-indicator';
            indicator.id = 'rangeIndicator';

            const cellRect = grid[y][x].element.getBoundingClientRect();
            const gridRect = document.getElementById('gameGrid').getBoundingClientRect();

            const cellSize = cellRect.width;
            const rangeSize = range * 2 * cellSize;

            indicator.style.width = rangeSize + 'px';
            indicator.style.height = rangeSize + 'px';
            indicator.style.left = (cellRect.left - gridRect.left + cellSize/2 - rangeSize/2) + 'px';
            indicator.style.top = (cellRect.top - gridRect.top + cellSize/2 - rangeSize/2) + 'px';

            document.getElementById('gameGrid').appendChild(indicator);
        }

        // Hide tower range indicator
        function hideTowerRange() {
            const indicator = document.getElementById('rangeIndicator');
            if (indicator) indicator.remove();
        }

        // Tower selection
        function selectTower(type) {
            // Clear previous selection
            document.querySelectorAll('.tower-btn').forEach(btn => btn.classList.remove('selected'));

            if (gameState.selectedTower === type) {
                gameState.selectedTower = null;
            } else {
                gameState.selectedTower = type;
                document.getElementById(`${type}TowerBtn`).classList.add('selected');
            }
        }

        // Start new wave
        function startWave() {
            if (gameState.enemiesLeft > 0 || gameState.isGameOver) return;

            gameState.wave++;
            gameState.enemiesInWave = Math.min(10 + gameState.wave * 2, 30);
            gameState.enemiesLeft = gameState.enemiesInWave;

            spawnWave();
            updateUI();
        }

        // Spawn monsters for current wave
        function spawnWave() {
            let spawnCount = 0;
            const spawnInterval = setInterval(() => {
                if (spawnCount >= gameState.enemiesInWave || gameState.isGameOver) {
                    clearInterval(spawnInterval);
                    return;
                }

                spawnMonster();
                spawnCount++;
            }, 1000);
        }

        // Spawn individual monster
        function spawnMonster() {
            const types = ['basic', 'fast', 'tank'];
            const weights = [0.6, 0.3, 0.1]; // Probability weights

            let random = Math.random();
            let selectedType = 'basic';

            for (let i = 0; i < types.length; i++) {
                if (random < weights[i]) {
                    selectedType = types[i];
                    break;
                }
                random -= weights[i];
            }

            const monsterType = MONSTER_TYPES[selectedType];
            const monster = {
                type: selectedType,
                health: monsterType.health * (1 + gameState.wave * 0.1), // Scale with wave
                maxHealth: monsterType.health * (1 + gameState.wave * 0.1),
                speed: monsterType.speed,
                reward: monsterType.reward,
                pathIndex: 0,
                x: path[0].x,
                y: path[0].y,
                element: createMonsterElement(selectedType),
                slowEffect: 0
            };

            const spawnCell = grid[monster.y][monster.x];
            spawnCell.element.appendChild(monster.element);
            monsters.push(monster);
        }

        // Create monster visual element
        function createMonsterElement(type) {
            const element = document.createElement('div');
            element.className = `monster ${type}`;

            const symbols = { basic: '👹', fast: '⚡', tank: '🛡️' };
            element.textContent = symbols[type];

            // Add health bar
            const healthBar = document.createElement('div');
            healthBar.className = 'health-bar';
            const healthFill = document.createElement('div');
            healthFill.className = 'health-fill';
            healthBar.appendChild(healthFill);
            element.appendChild(healthBar);

            return element;
        }

        // Game loop
        function gameLoop() {
            if (gameState.isPaused || gameState.isGameOver) return;

            moveMonsters();
            updateTowers();
            updateProjectiles();
            checkWaveComplete();

            requestAnimationFrame(gameLoop);
        }

        // Move monsters along path
        function moveMonsters() {
            monsters.forEach((monster, index) => {
                if (monster.pathIndex >= path.length - 1) {
                    // Monster reached village
                    gameState.health -= 10;
                    removeMonster(index);

                    if (gameState.health <= 0) {
                        gameOver();
                    }
                    return;
                }

                const currentPos = path[monster.pathIndex];
                const nextPos = path[monster.pathIndex + 1];

                if (!nextPos) return;

                // Calculate movement
                const dx = nextPos.x - currentPos.x;
                const dy = nextPos.y - currentPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                const speed = monster.speed * (monster.slowEffect > 0 ? 0.5 : 1);
                const moveDistance = speed * 0.02; // Adjust speed

                if (distance <= moveDistance) {
                    // Move to next path point
                    monster.pathIndex++;
                    monster.x = nextPos.x;
                    monster.y = nextPos.y;

                    // Move element to new cell
                    const newCell = grid[monster.y][monster.x];
                    newCell.element.appendChild(monster.element);
                } else {
                    // Interpolate position
                    const ratio = moveDistance / distance;
                    monster.x += dx * ratio;
                    monster.y += dy * ratio;

                    // Update visual position
                    const cellSize = grid[0][0].element.offsetWidth;
                    monster.element.style.left = (monster.x * cellSize) + 'px';
                    monster.element.style.top = (monster.y * cellSize) + 'px';
                }

                // Reduce slow effect
                if (monster.slowEffect > 0) {
                    monster.slowEffect--;
                }
            });
        }

        // Update tower behavior
        function updateTowers() {
            const currentTime = Date.now();

            towers.forEach(tower => {
                // Find target
                if (!tower.target || !isValidTarget(tower, tower.target)) {
                    tower.target = findNearestMonster(tower);
                }

                // Fire at target
                if (tower.target && currentTime - tower.lastFire > tower.fireRate) {
                    fireProjectile(tower, tower.target);
                    tower.lastFire = currentTime;
                }
            });
        }

        // Find nearest monster in range
        function findNearestMonster(tower) {
            let nearest = null;
            let minDistance = Infinity;

            monsters.forEach(monster => {
                const distance = Math.sqrt(
                    Math.pow(monster.x - tower.x, 2) +
                    Math.pow(monster.y - tower.y, 2)
                );

                if (distance <= tower.range && distance < minDistance) {
                    nearest = monster;
                    minDistance = distance;
                }
            });

            return nearest;
        }

        // Check if target is still valid
        function isValidTarget(tower, monster) {
            const distance = Math.sqrt(
                Math.pow(monster.x - tower.x, 2) +
                Math.pow(monster.y - tower.y, 2)
            );
            return distance <= tower.range && monster.health > 0;
        }

        // Fire projectile
        function fireProjectile(tower, target) {
            const projectile = {
                x: tower.x,
                y: tower.y,
                targetX: target.x,
                targetY: target.y,
                target: target,
                tower: tower,
                speed: 0.1,
                element: createProjectileElement()
            };

            document.getElementById('gameGrid').appendChild(projectile.element);
            projectiles.push(projectile);
        }

        // Create projectile visual element
        function createProjectileElement() {
            const element = document.createElement('div');
            element.className = 'projectile';
            return element;
        }

        // Update projectiles
        function updateProjectiles() {
            projectiles.forEach((projectile, index) => {
                const dx = projectile.targetX - projectile.x;
                const dy = projectile.targetY - projectile.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 0.1) {
                    // Hit target
                    hitTarget(projectile);
                    removeProjectile(index);
                } else {
                    // Move projectile
                    projectile.x += (dx / distance) * projectile.speed;
                    projectile.y += (dy / distance) * projectile.speed;

                    // Update visual position
                    const cellSize = grid[0][0].element.offsetWidth;
                    projectile.element.style.left = (projectile.x * cellSize) + 'px';
                    projectile.element.style.top = (projectile.y * cellSize) + 'px';
                }
            });
        }

        // Handle projectile hitting target
        function hitTarget(projectile) {
            const tower = projectile.tower;
            const target = projectile.target;

            // Create explosion effect
            createExplosion(projectile.targetX, projectile.targetY);

            if (tower.type === 'splash') {
                // Splash damage
                monsters.forEach(monster => {
                    const distance = Math.sqrt(
                        Math.pow(monster.x - projectile.targetX, 2) +
                        Math.pow(monster.y - projectile.targetY, 2)
                    );

                    if (distance <= 1.5) {
                        damageMonster(monster, tower.damage * (distance <= 0.5 ? 1 : 0.5));
                    }
                });
            } else {
                // Single target damage
                damageMonster(target, tower.damage);

                if (tower.type === 'slow') {
                    target.slowEffect = 60; // 60 frames of slow
                }
            }
        }

        // Damage monster
        function damageMonster(monster, damage) {
            monster.health -= damage;

            // Update health bar
            const healthPercent = Math.max(0, monster.health / monster.maxHealth);
            const healthFill = monster.element.querySelector('.health-fill');
            if (healthFill) {
                healthFill.style.width = (healthPercent * 100) + '%';
            }

            if (monster.health <= 0) {
                // Monster defeated
                gameState.coins += monster.reward;
                gameState.score += monster.reward * 10;
                gameState.enemiesLeft--;

                const monsterIndex = monsters.indexOf(monster);
                if (monsterIndex > -1) {
                    removeMonster(monsterIndex);
                }
            }
        }

        // Create explosion effect
        function createExplosion(x, y) {
            const explosion = document.createElement('div');
            explosion.className = 'explosion';

            const cellSize = grid[0][0].element.offsetWidth;
            explosion.style.left = (x * cellSize - 15) + 'px';
            explosion.style.top = (y * cellSize - 15) + 'px';

            document.getElementById('gameGrid').appendChild(explosion);

            setTimeout(() => {
                if (explosion.parentNode) {
                    explosion.parentNode.removeChild(explosion);
                }
            }, 300);
        }

        // Remove monster
        function removeMonster(index) {
            const monster = monsters[index];
            if (monster && monster.element && monster.element.parentNode) {
                monster.element.parentNode.removeChild(monster.element);
            }
            monsters.splice(index, 1);
        }

        // Remove projectile
        function removeProjectile(index) {
            const projectile = projectiles[index];
            if (projectile && projectile.element && projectile.element.parentNode) {
                projectile.element.parentNode.removeChild(projectile.element);
            }
            projectiles.splice(index, 1);
        }

        // Check if wave is complete
        function checkWaveComplete() {
            if (gameState.enemiesLeft <= 0 && monsters.length === 0) {
                // Wave complete
                gameState.coins += 50; // Bonus for completing wave

                if (gameState.wave >= 20) {
                    // Victory condition
                    victory();
                }
            }
        }

        // Game over
        function gameOver() {
            gameState.isGameOver = true;
            document.getElementById('gameOver').style.display = 'block';
            document.getElementById('gameOverTitle').textContent = translations[gameState.language].gameOver;
            document.getElementById('gameOverText').textContent = translations[gameState.language].villageDestroyed;
        }

        // Victory
        function victory() {
            gameState.isGameOver = true;
            document.getElementById('gameOver').style.display = 'block';
            document.getElementById('gameOverTitle').textContent = translations[gameState.language].victory;
            document.getElementById('gameOverText').textContent = translations[gameState.language].allWavesDefeated;
        }

        // Restart game
        function restartGame() {
            // Reset game state
            gameState = {
                health: 100,
                coins: 100,
                score: 0,
                wave: 1,
                enemiesInWave: 10,
                enemiesLeft: 10,
                selectedTower: null,
                isPaused: false,
                isGameOver: false,
                language: gameState.language
            };

            // Clear arrays
            towers = [];
            monsters = [];
            projectiles = [];

            // Clear visual elements
            document.querySelectorAll('.tower, .monster, .projectile, .explosion').forEach(el => el.remove());

            // Hide game over screen
            document.getElementById('gameOver').style.display = 'none';

            // Reinitialize
            initGame();
        }

        // Toggle pause
        function togglePause() {
            gameState.isPaused = !gameState.isPaused;
            const pauseBtn = document.getElementById('pauseBtn');
            const pauseText = document.getElementById('pauseText');

            if (gameState.isPaused) {
                pauseText.textContent = translations[gameState.language].resume;
            } else {
                pauseText.textContent = translations[gameState.language].pause;
                gameLoop(); // Resume game loop
            }
        }

        // Update UI
        function updateUI() {
            document.getElementById('healthValue').textContent = gameState.health;
            document.getElementById('coinsValue').textContent = gameState.coins;
            document.getElementById('scoreValue').textContent = gameState.score;
            document.getElementById('waveNumber').textContent = gameState.wave;
            document.getElementById('enemiesLeft').textContent = gameState.enemiesLeft;

            // Update tower button states
            Object.keys(TOWER_TYPES).forEach(type => {
                const btn = document.getElementById(`${type}TowerBtn`);
                btn.disabled = gameState.coins < TOWER_TYPES[type].cost;
            });

            // Update start wave button
            const startBtn = document.getElementById('startWaveBtn');
            startBtn.disabled = gameState.enemiesLeft > 0 || gameState.isGameOver;
        }

        // Toggle language
        function toggleLanguage() {
            gameState.language = gameState.language === 'uk' ? 'en' : 'uk';
            updateLanguage();

            const langBtn = document.querySelector('.language-toggle');
            langBtn.textContent = gameState.language === 'uk' ? 'EN' : 'УК';
        }

        // Update language
        function updateLanguage() {
            const lang = translations[gameState.language];

            document.getElementById('healthText').textContent = lang.health;
            document.getElementById('coinsText').textContent = lang.coins;
            document.getElementById('scoreText').textContent = lang.score;
            document.getElementById('waveText').textContent = lang.wave;
            document.getElementById('enemiesText').textContent = lang.enemies;
            document.getElementById('shopTitle').textContent = lang.shopTitle;
            document.getElementById('basicTowerText').textContent = lang.basicTower;
            document.getElementById('splashTowerText').textContent = lang.splashTower;
            document.getElementById('slowTowerText').textContent = lang.slowTower;
            document.getElementById('startWaveText').textContent = lang.startWave;
            document.getElementById('pauseText').textContent = gameState.isPaused ? lang.resume : lang.pause;
            document.getElementById('restartBtn').textContent = lang.restart;
        }

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    togglePause();
                    break;
                case '1':
                    selectTower('basic');
                    break;
                case '2':
                    selectTower('splash');
                    break;
                case '3':
                    selectTower('slow');
                    break;
                case 'Escape':
                    gameState.selectedTower = null;
                    document.querySelectorAll('.tower-btn').forEach(btn => btn.classList.remove('selected'));
                    break;
            }
        });

        // Start the game when page loads
        window.addEventListener('load', () => {
            initGame();
            gameLoop();
        });
    </script>
</body>
</html>
